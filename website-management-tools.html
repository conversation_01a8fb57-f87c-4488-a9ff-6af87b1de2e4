<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <title>Website Management Tools – Encode, Minify, Beautify HTML, CSS, JavaScript</title>
  <meta name="description" content="Improve your website performance with our free online tools. Minify HTML/CSS/JS, encode URLs, generate QR codes, and enhance your site's SEO readiness.">
  <meta name="keywords" content="website management tools, html minifier, css minifier, javascript minifier, url encoder, qr code generator, html beautifier">
  <link rel="canonical" href="https://www.webtoolskit.org/p/website-managements.html">

  <!-- Page-specific Open Graph Meta Tags -->
  <meta property="og:url" content="https://www.webtoolskit.org/p/website-managements.html">
  <meta property="og:title" content="Website Management Tools - Minify, Encode, Generate & Optimize">
  <meta property="og:description" content="Improve your website performance with our free online tools. Minify HTML/CSS/JS, encode URLs, generate QR codes, and enhance your site's SEO readiness.">
  <meta property="og:image" content="https://www.webtoolskit.org/images/websites-og.jpg">

  <!-- Page-specific Twitter Card Meta Tags -->
  <meta name="twitter:url" content="https://www.webtoolskit.org/p/website-managements.html">
  <meta name="twitter:title" content="Website Management Tools - Minify, Encode, Generate & Optimize">
  <meta name="twitter:description" content="Improve your website performance with our free online tools. Minify HTML/CSS/JS, encode URLs, generate QR codes, and enhance your site's SEO readiness.">
  <meta name="twitter:image" content="https://www.webtoolskit.org/images/websites-og.jpg">
  
  <!-- Enhanced Schema.org structured data -->
  <script type="application/ld+json">
  {
    "@context": "https://schema.org",
    "@type": ["WebPage", "CollectionPage"],
    "name": "Website Management Tools – Encode, Minify, Beautify HTML, CSS, JavaScript",
    "description": "Improve your website performance with our free online tools. Minify HTML/CSS/JS, encode URLs, generate QR codes, and enhance your site's SEO readiness.",
    "url": "https://www.webtoolskit.org/p/website-managements.html",
    "isAccessibleForFree": true,
    "datePublished": "2025-06-26",
    "dateModified": "2025-06-26",
    "author": { "@type": "Organization", "name": "WebToolsKit", "url": "https://www.webtoolskit.org", "logo": { "@type": "ImageObject", "url": "https://www.webtoolskit.org/images/logo.png" }},
    "publisher": { "@type": "Organization", "name": "WebToolsKit", "url": "https://www.webtoolskit.org" },
    "mainEntity": {
      "@type": "ItemList",
      "name": "Website Management Tools Collection",
      "description": "A collection of free online tools for website optimization, encoding, code formatting, and marketing.",
      "numberOfItems": 18,
      "itemListElement": [
        { "@type": "WebApplication", "position": 1, "name": "HTML Decode", "description": "Decode HTML entities back to readable characters for content processing and display.", "url": "https://www.webtoolskit.org/p/html-decode.html", "applicationCategory": "DeveloperTool", "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" }, "featureList": ["HTML entity decoding", "Character conversion"] },
        { "@type": "WebApplication", "position": 2, "name": "HTML Encode", "description": "Encode special characters to HTML entities for safe display in web pages.", "url": "https://www.webtoolskit.org/p/html-encode.html", "applicationCategory": "DeveloperTool", "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" }, "featureList": ["HTML entity encoding", "Web safety"] },
        { "@type": "WebApplication", "position": 3, "name": "URL Decode", "description": "Decode URL-encoded strings back to readable format for analysis and debugging.", "url": "https://www.webtoolskit.org/p/url-decode.html", "applicationCategory": "DeveloperTool", "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" }, "featureList": ["URL decoding", "Percent-encoding to text"] },
        { "@type": "WebApplication", "position": 4, "name": "URL Encode", "description": "Encode special characters in URLs to ensure proper transmission and compatibility.", "url": "https://www.webtoolskit.org/p/url-encode.html", "applicationCategory": "DeveloperTool", "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" }, "featureList": ["URL encoding", "Percent-encoding"] },
        { "@type": "WebApplication", "position": 5, "name": "HTML Beautifier", "description": "Format and beautify HTML code with proper indentation and structure.", "url": "https://www.webtoolskit.org/p/html-beautifier.html", "applicationCategory": "DeveloperTool", "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" }, "featureList": ["Code formatting", "Indentation", "Readability improvement"] },
        { "@type": "WebApplication", "position": 6, "name": "HTML Minifier", "description": "Minify HTML code to reduce file size and improve website loading speed.", "url": "https://www.webtoolskit.org/p/html-minifier.html", "applicationCategory": "DeveloperTool", "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" }, "featureList": ["Code compression", "File size reduction", "Performance optimization"] },
        { "@type": "WebApplication", "position": 7, "name": "CSS Minifier", "description": "Compress CSS files by removing whitespace and comments for faster loading.", "url": "https://www.webtoolskit.org/p/css-minifier.html", "applicationCategory": "DeveloperTool", "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" }, "featureList": ["CSS compression", "File size reduction", "Website speed"] },
        { "@type": "WebApplication", "position": 8, "name": "QR Code Generator", "description": "Generate QR codes for URLs, text, contact info, and other data types.", "url": "https://www.webtoolskit.org/p/qr-code-generator.html", "applicationCategory": "UtilityApplication", "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" }, "featureList": ["QR code creation", "URL/Text/Contact to QR", "Marketing tool"] },
        { "@type": "WebApplication", "position": 9, "name": "CSS Beautifier", "description": "Format and beautify CSS code with proper indentation and structure for better readability.", "url": "https://www.webtoolskit.org/p/css-beautifier.html", "applicationCategory": "DeveloperTool", "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" }, "featureList": ["CSS formatting", "Pretty print", "Code readability"] },
        { "@type": "WebApplication", "position": 10, "name": "JavaScript Beautifier", "description": "Format and beautify JavaScript code with proper indentation and structure.", "url": "https://www.webtoolskit.org/p/javascript-beautifier.html", "applicationCategory": "DeveloperTool", "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" }, "featureList": ["JS code formatting", "Syntax highlighting", "Debugging aid"] },
        { "@type": "WebApplication", "position": 11, "name": "JavaScript Minifier", "description": "Minify JavaScript code to reduce file size and improve website performance.", "url": "https://www.webtoolskit.org/p/javascript-minifier.html", "applicationCategory": "DeveloperTool", "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" }, "featureList": ["JS compression", "Code minification", "Performance enhancement"] },
        { "@type": "WebApplication", "position": 12, "name": "Javascript DeObfuscator", "description": "Deobfuscate and format obfuscated JavaScript code for analysis and debugging.", "url": "https://www.webtoolskit.org/p/javascript-deobfuscator.html", "applicationCategory": "DeveloperTool", "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" }, "featureList": ["JS deobfuscation", "Code analysis", "Reverse engineering aid"] },
        { "@type": "WebApplication", "position": 13, "name": "Javascript Obfuscator", "description": "Obfuscate JavaScript code to protect intellectual property and prevent reverse engineering.", "url": "https://www.webtoolskit.org/p/javascript-obfuscator.html", "applicationCategory": "DeveloperTool", "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" }, "featureList": ["JS obfuscation", "Code protection", "Security tool"] },
        { "@type": "WebApplication", "position": 14, "name": "QR Code Decoder", "description": "Decode and read QR codes from images to extract embedded information.", "url": "https://www.webtoolskit.org/p/qr-code-decoder.html", "applicationCategory": "UtilityApplication", "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" }, "featureList": ["QR code reader", "Image to text/URL", "Data extraction"] },
        { "@type": "WebApplication", "position": 15, "name": "Find Facebook ID", "description": "Find Facebook user ID or page ID from profile URLs for social media management.", "url": "https://www.webtoolskit.org/p/find-facebook-id.html", "applicationCategory": "UtilityApplication", "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" }, "featureList": ["Facebook ID lookup", "Social media utility"] },
        { "@type": "WebApplication", "position": 16, "name": "UUID Generator", "description": "Generate unique identifiers (UUIDs) for databases, APIs, and application development.", "url": "https://www.webtoolskit.org/p/uuid-generator.html", "applicationCategory": "DeveloperTool", "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" }, "featureList": ["UUID generation", "Unique ID creation", "Version 4 UUID"] },
        { "@type": "WebApplication", "position": 17, "name": "URL Parser", "description": "Parse and analyze URLs to extract components like protocol, domain, path, and parameters.", "url": "https://www.webtoolskit.org/p/url-parser.html", "applicationCategory": "DeveloperTool", "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" }, "featureList": ["URL component extraction", "Parameter analysis", "Debugging tool"] },
        { "@type": "WebApplication", "position": 18, "name": "UTM Builder", "description": "Build UTM tracking URLs for campaign tracking and analytics measurement.", "url": "https://www.webtoolskit.org/p/utm-builder.html", "applicationCategory": "BusinessApplication", "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" }, "featureList": ["UTM parameter generation", "Campaign tracking", "Marketing analytics"] }
      ]
    },
    "breadcrumb": {
      "@type": "BreadcrumbList",
      "itemListElement": [
        { "@type": "ListItem", "position": 1, "name": "Home", "item": "https://www.webtoolskit.org" },
        { "@type": "ListItem", "position": 2, "name": "Website Management Tools" }
      ]
    }
  }
  </script>

  <style>
    /* Duplicate CSS variables and base styles removed - inherit from main template */

    /* Page-specific styles only */
    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 8px 16px;
    }

    .page-header {
      text-align: center;
      margin-bottom: 20px;
    }

    .page-title {
      font-size: 36px;
      font-weight: 700;
      color: var(--primary-color);
      margin-bottom: 10px;
      line-height: 1.2;
    }

    .page-description {
      font-size: 1.1rem;
      color: var(--text-color-light);
      max-width: 600px;
      margin: 0 auto;
      line-height: 1.5;
    }

    .tools-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
      gap: 16px;
      justify-items: center;
    }

    .tool-card {
      background: var(--card-bg);
      border: 1px solid var(--border-color);
      border-radius: 10px;
      padding: 18px;
      text-align: center;
      transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
      box-shadow: 0 2px 8px rgba(0,0,0,0.08);
      position: relative;
      overflow: hidden;
      cursor: pointer;
    }

    .tool-card::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 4px;
      background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
      opacity: 0;
      transition: opacity 0.4s ease;
    }

    .tool-card::after {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
      transition: left 0.6s ease;
    }

    .tool-card:hover {
      transform: translateY(-8px) scale(1.02);
      box-shadow: 0 12px 35px rgba(0,0,0,0.2);
      border-color: var(--primary-color);
    }

    .tool-card:hover::before {
      opacity: 1;
    }

    .tool-card:hover::after {
      left: 100%;
    }

    .tool-icon {
      width: 56px;
      height: 56px;
      border-radius: 10px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0 auto 12px;
      font-size: 22px;
      transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
      position: relative;
      overflow: hidden;
    }

    .tool-icon::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(135deg, rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0.1));
      opacity: 0;
      transition: opacity 0.3s ease;
    }

    .tool-card:hover .tool-icon {
      transform: scale(1.1) rotate(5deg);
      box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
    }

    .tool-card:hover .tool-icon::before {
      opacity: 1;
    }

    /* Distinctive Icon Colors for Website Management Tools */
    .icon-html-decode { background: linear-gradient(135deg, #2563eb, #1d4ed8); color: white; }
    .icon-html-encode { background: linear-gradient(135deg, #8B5CF6, #7C3AED); color: white; }
    .icon-url-decode { background: linear-gradient(135deg, #EC4899, #DB2777); color: white; }
    .icon-url-encode { background: linear-gradient(135deg, #10B981, #059669); color: white; }
    .icon-html-beautifier { background: linear-gradient(135deg, #F59E0B, #D97706); color: white; }
    .icon-html-minifier { background: linear-gradient(135deg, #6366F1, #4F46E5); color: white; }
    .icon-css-beautifier { background: linear-gradient(135deg, #0EA5E9, #0284C7); color: white; }
    .icon-css-minifier { background: linear-gradient(135deg, #4F46E5, #4338CA); color: white; }
    .icon-js-beautifier { background: linear-gradient(135deg, #EF4444, #DC2626); color: white; }
    .icon-js-minifier { background: linear-gradient(135deg, #14B8A6, #0D9488); color: white; }
    .icon-js-deobfuscator { background: linear-gradient(135deg, #06B6D4, #0891B2); color: white; }
    .icon-js-obfuscator { background: linear-gradient(135deg, #84CC16, #65A30D); color: white; }
    .icon-qr-decoder { background: linear-gradient(135deg, #A855F7, #9333EA); color: white; }
    .icon-qr-generator { background: linear-gradient(135deg, #8B5CF6, #7C3AED); color: white; }
    .icon-facebook-id { background: linear-gradient(135deg, #3B82F6, #2563EB); color: white; }
    .icon-uuid-generator { background: linear-gradient(135deg, #F97316, #EA580C); color: white; }
    .icon-url-parser { background: linear-gradient(135deg, #EC4899, #DB2777); color: white; }
    .icon-utm-builder { background: linear-gradient(135deg, #10B981, #059669); color: white; }

    .tool-title {
      font-size: 18px;
      font-weight: 600;
      color: var(--text-color);
      margin-bottom: 8px;
    }

    .tool-description {
      color: var(--text-color-light);
      font-size: 14px;
      margin-bottom: 12px;
      line-height: 1.4;
    }

    .tool-link {
      display: inline-block;
      background: var(--primary-color);
      color: #ffffff !important;
      text-decoration: none;
      padding: 12px 24px;
      border-radius: 8px;
      font-weight: 600;
      font-size: 14px;
      transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
      border: 2px solid var(--primary-color);
      box-shadow: 0 2px 8px rgba(0, 71, 171, 0.2);
      position: relative;
      overflow: hidden;
    }

    .tool-link::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
      transition: left 0.5s ease;
    }

    .tool-link:hover {
      background: #003d96;
      border-color: #003d96;
      transform: translateY(-3px) scale(1.05);
      box-shadow: 0 8px 25px rgba(0, 71, 171, 0.4);
      color: #ffffff !important;
    }

    .tool-link:hover::before {
      left: 100%;
    }

    [data-theme="dark"] .tool-link {
      background: #60a5fa;
      border-color: #60a5fa;
      color: #ffffff !important;
      box-shadow: 0 2px 8px rgba(96, 165, 250, 0.2);
    }

    [data-theme="dark"] .tool-link:hover {
      background: #3b82f6;
      border-color: #3b82f6;
      box-shadow: 0 4px 12px rgba(96, 165, 250, 0.3);
      color: #ffffff !important;
    }

    /* Mobile Optimization with Scrollable Icons */
    @media (max-width: 768px) {
      .container { padding: 6px 12px; }
      .page-header { margin-bottom: 16px; }
      .page-title { font-size: 28px; margin-bottom: 8px; }
      .page-description { font-size: 1rem; padding: 0 8px; }

      .tools-grid { display: block; overflow-x: unset; padding: 8px 0; }
      .tool-card { width: 100%; margin: 0 0 12px 0; border-radius: 14px; padding: 16px; min-height: 80px; box-sizing: border-box; }
      .tool-card .tool-icon { width: 48px; height: 48px; margin: 0 auto 8px; font-size: 20px; }
      .tool-card .tool-title, .tool-card .tool-link { opacity: 1; transform: none; pointer-events: auto; }
      .tool-card .tool-description { opacity: 0; max-height: 0; overflow: hidden; margin: 0; transition: opacity 0.3s, max-height 0.3s; will-change: opacity, max-height; display: block; }
      .tool-card.show-description .tool-description { opacity: 1; max-height: 100px; margin-bottom: 10px; }
    }
    @media (max-width: 480px) {
      .tool-card { min-width: 200px; max-width: 95vw; padding: 10px; }
      .tool-card .tool-icon { width: 40px; height: 40px; font-size: 18px; }
    }
    @media (max-width: 320px) {
      .tool-card { min-width: 140px; padding: 6px; }
      .tool-card .tool-icon { width: 32px; height: 32px; font-size: 15px; }
    }
  </style>
</head>
<body>
  <div class="container">
    <header class="page-header">
      <h1 id="main-title" class="page-title">Website Management Tools – Encode, Minify, Generate &amp; Optimize</h1>
      <p class="page-description">Improve your website performance with our free online tools. Minify HTML/CSS/JS, encode URLs, generate QR codes, and enhance your site's SEO readiness.</p>
    </header>

    <main id="main-content" role="main" aria-labelledby="main-title">
      <section class="tools-section" aria-labelledby="tools-section-title">
        <h2 id="tools-section-title" class="sr-only">Available Website Management Tools</h2>
        <div class="tools-grid" role="list">
          <!-- HTML Decode -->
          <article class="tool-card" role="listitem">
            <div class="tool-icon icon-html-decode" aria-hidden="true"><i class="fas fa-unlock"></i></div>
            <div class="tool-content">
              <h3 class="tool-title">HTML Decode</h3>
              <p class="tool-description">Decode HTML entities back to readable characters for content processing and display.</p>
              <a class="tool-link" href="/p/html-decode.html" rel="noopener">Try this tool →</a>
            </div>
          </article>
          <!-- HTML Encode -->
          <article class="tool-card" role="listitem">
            <div class="tool-icon icon-html-encode" aria-hidden="true"><i class="fas fa-lock"></i></div>
            <div class="tool-content">
              <h3 class="tool-title">HTML Encode</h3>
              <p class="tool-description">Encode special characters to HTML entities for safe display in web pages.</p>
              <a class="tool-link" href="/p/html-encode.html" rel="noopener">Try this tool →</a>
            </div>
          </article>
          <!-- URL Decode -->
          <article class="tool-card" role="listitem">
            <div class="tool-icon icon-url-decode" aria-hidden="true"><i class="fas fa-link"></i></div>
            <div class="tool-content">
              <h3 class="tool-title">URL Decode</h3>
              <p class="tool-description">Decode URL-encoded strings back to readable format for analysis and debugging.</p>
              <a class="tool-link" href="/p/url-decode.html" rel="noopener">Try this tool →</a>
            </div>
          </article>
          <!-- URL Encode -->
          <article class="tool-card" role="listitem">
            <div class="tool-icon icon-url-encode" aria-hidden="true"><i class="fas fa-shield-alt"></i></div>
            <div class="tool-content">
              <h3 class="tool-title">URL Encode</h3>
              <p class="tool-description">Encode special characters in URLs to ensure proper transmission and compatibility.</p>
              <a class="tool-link" href="/p/url-encode.html" rel="noopener">Try this tool →</a>
            </div>
          </article>
          <!-- HTML Beautifier -->
          <article class="tool-card" role="listitem">
            <div class="tool-icon icon-html-beautifier" aria-hidden="true"><i class="fas fa-code"></i></div>
            <div class="tool-content">
              <h3 class="tool-title">HTML Beautifier</h3>
              <p class="tool-description">Format and beautify HTML code with proper indentation and structure.</p>
              <a class="tool-link" href="/p/html-beautifier.html" rel="noopener">Try this tool →</a>
            </div>
          </article>
          <!-- HTML Minifier -->
          <article class="tool-card" role="listitem">
            <div class="tool-icon icon-html-minifier" aria-hidden="true"><i class="fas fa-compress"></i></div>
            <div class="tool-content">
              <h3 class="tool-title">HTML Minifier</h3>
              <p class="tool-description">Minify HTML code to reduce file size and improve website loading speed.</p>
              <a class="tool-link" href="/p/html-minifier.html" rel="noopener">Try this tool →</a>
            </div>
          </article>
          <!-- CSS Minifier -->
          <article class="tool-card" role="listitem">
            <div class="tool-icon icon-css-minifier" aria-hidden="true"><i class="fas fa-file-code"></i></div>
            <div class="tool-content">
              <h3 class="tool-title">CSS Minifier</h3>
              <p class="tool-description">Compress CSS files by removing whitespace and comments for faster loading.</p>
              <a class="tool-link" href="/p/css-minifier.html" rel="noopener">Try this tool →</a>
            </div>
          </article>
          <!-- QR Code Generator -->
          <article class="tool-card" role="listitem">
            <div class="tool-icon icon-qr-generator" aria-hidden="true"><i class="fas fa-qrcode"></i></div>
            <div class="tool-content">
              <h3 class="tool-title">QR Code Generator</h3>
              <p class="tool-description">Generate QR codes for URLs, text, contact info, and other data types.</p>
              <a class="tool-link" href="/p/qr-code-generator.html" rel="noopener">Try this tool →</a>
            </div>
          </article>
          <!-- CSS Beautifier -->
          <article class="tool-card" role="listitem">
            <div class="tool-icon icon-css-beautifier" aria-hidden="true"><i class="fas fa-palette"></i></div>
            <div class="tool-content">
              <h3 class="tool-title">CSS Beautifier</h3>
              <p class="tool-description">Format and beautify CSS code with proper indentation and structure for better readability.</p>
              <a class="tool-link" href="/p/css-beautifier.html" rel="noopener">Try this tool →</a>
            </div>
          </article>
          <!-- JavaScript Beautifier -->
          <article class="tool-card" role="listitem">
            <div class="tool-icon icon-js-beautifier" aria-hidden="true"><i class="fas fa-code"></i></div>
            <div class="tool-content">
              <h3 class="tool-title">JavaScript Beautifier</h3>
              <p class="tool-description">Format and beautify JavaScript code with proper indentation and structure.</p>
              <a class="tool-link" href="/p/javascript-beautifier.html" rel="noopener">Try this tool →</a>
            </div>
          </article>
          <!-- JavaScript Minifier -->
          <article class="tool-card" role="listitem">
            <div class="tool-icon icon-js-minifier" aria-hidden="true"><i class="fas fa-compress-alt"></i></div>
            <div class="tool-content">
              <h3 class="tool-title">JavaScript Minifier</h3>
              <p class="tool-description">Minify JavaScript code to reduce file size and improve website performance.</p>
              <a class="tool-link" href="/p/javascript-minifier.html" rel="noopener">Try this tool →</a>
            </div>
          </article>
          <!-- Javascript DeObfuscator -->
          <article class="tool-card" role="listitem">
            <div class="tool-icon icon-js-deobfuscator" aria-hidden="true"><i class="fas fa-unlock-alt"></i></div>
            <div class="tool-content">
              <h3 class="tool-title">Javascript DeObfuscator</h3>
              <p class="tool-description">Deobfuscate and format obfuscated JavaScript code for analysis and debugging.</p>
              <a class="tool-link" href="/p/javascript-deobfuscator.html" rel="noopener">Try this tool →</a>
            </div>
          </article>
          <!-- Javascript Obfuscator -->
          <article class="tool-card" role="listitem">
            <div class="tool-icon icon-js-obfuscator" aria-hidden="true"><i class="fas fa-shield-alt"></i></div>
            <div class="tool-content">
              <h3 class="tool-title">Javascript Obfuscator</h3>
              <p class="tool-description">Obfuscate JavaScript code to protect intellectual property and prevent reverse engineering.</p>
              <a class="tool-link" href="/p/javascript-obfuscator.html" rel="noopener">Try this tool →</a>
            </div>
          </article>
          <!-- QR Code Decoder -->
          <article class="tool-card" role="listitem">
            <div class="tool-icon icon-qr-decoder" aria-hidden="true"><i class="fas fa-search"></i></div>
            <div class="tool-content">
              <h3 class="tool-title">QR Code Decoder</h3>
              <p class="tool-description">Decode and read QR codes from images to extract embedded information.</p>
              <a class="tool-link" href="/p/qr-code-decoder.html" rel="noopener">Try this tool →</a>
            </div>
          </article>
          <!-- Find Facebook ID -->
          <article class="tool-card" role="listitem">
            <div class="tool-icon icon-facebook-id" aria-hidden="true"><i class="fab fa-facebook"></i></div>
            <div class="tool-content">
              <h3 class="tool-title">Find Facebook ID</h3>
              <p class="tool-description">Find Facebook user ID or page ID from profile URLs for social media management.</p>
              <a class="tool-link" href="/p/find-facebook-id.html" rel="noopener">Try this tool →</a>
            </div>
          </article>
          <!-- UUID Generator -->
          <article class="tool-card" role="listitem">
            <div class="tool-icon icon-uuid-generator" aria-hidden="true"><i class="fas fa-fingerprint"></i></div>
            <div class="tool-content">
              <h3 class="tool-title">UUID Generator</h3>
              <p class="tool-description">Generate unique identifiers (UUIDs) for databases, APIs, and application development.</p>
              <a class="tool-link" href="/p/uuid-generator.html" rel="noopener">Try this tool →</a>
            </div>
          </article>
          <!-- URL Parser -->
          <article class="tool-card" role="listitem">
            <div class="tool-icon icon-url-parser" aria-hidden="true"><i class="fas fa-link"></i></div>
            <div class="tool-content">
              <h3 class="tool-title">URL Parser</h3>
              <p class="tool-description">Parse and analyze URLs to extract components like protocol, domain, path, and parameters.</p>
              <a class="tool-link" href="/p/url-parser.html" rel="noopener">Try this tool →</a>
            </div>
          </article>
          <!-- UTM Builder -->
          <article class="tool-card" role="listitem">
            <div class="tool-icon icon-utm-builder" aria-hidden="true"><i class="fas fa-chart-line"></i></div>
            <div class="tool-content">
              <h3 class="tool-title">UTM Builder</h3>
              <p class="tool-description">Build UTM tracking URLs for campaign tracking and analytics measurement.</p>
              <a class="tool-link" href="/p/utm-builder.html" rel="noopener">Try this tool →</a>
            </div>
          </article>
        </div>
      </section>
    </main>
  </div>
  
  <script>
    // --- SIMPLIFIED: Expand card on tap for mobile ---
    document.addEventListener('DOMContentLoaded', function() {
      if (window.innerWidth > 768) return;
      const toolCards = document.querySelectorAll('.tool-card');
      toolCards.forEach(card => {
        card.addEventListener('click', function() {
          toolCards.forEach(c => c.classList.remove('expanded'));
          this.classList.add('expanded');
        });
      });
    });

    // Mobile: Description toggles on click/tap, always works instantly
    document.addEventListener('DOMContentLoaded', function() {
      if (window.innerWidth > 768) return;
      const toolCards = document.querySelectorAll('.tool-card');
      // Prevent double event firing on touch devices
      let lastTouch = 0;
      toolCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
          this.classList.add('show-description');
        });
        card.addEventListener('mouseleave', function() {
          this.classList.remove('show-description');
        });
        card.addEventListener('touchend', function(e) {
          e.preventDefault();
          lastTouch = Date.now();
          if (this.classList.contains('show-description')) {
            this.classList.remove('show-description');
          } else {
            toolCards.forEach(c => c.classList.remove('show-description'));
            this.classList.add('show-description');
          }
        }, { passive: false });
        card.addEventListener('click', function(e) {
          // Ignore click if just handled by touch
          if (Date.now() - lastTouch < 500) return;
          if (this.classList.contains('show-description')) {
            this.classList.remove('show-description');
          } else {
            toolCards.forEach(c => c.classList.remove('show-description'));
            this.classList.add('show-description');
          }
        });
      });
    });
  </script>

</body>
</html>