<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <title>Free Image Editing Tools – Resize, Convert, Compress & Edit Online</title>
  <meta name="description" content="Edit images online with ease. Convert formats, crop, resize, rotate, or compress images instantly. No software required — fast, free, and beginner-friendly.">
  <meta name="keywords" content="image editing tools, image converter, image resizer, image cropper, image format converter, online image editor, free image tools">
  <link rel="canonical" href="https://www.webtoolskit.org/p/image-editing-tools.html">

  <!-- Page-specific Open Graph Meta Tags -->
  <meta property="og:url" content="https://www.webtoolskit.org/p/image-editing-tools.html">
  <meta property="og:title" content="Free Image Editing Tools - Online Image Editor">
  <meta property="og:description" content="Edit images online with ease. Convert formats, crop, resize, rotate, or compress images instantly. No software required — fast, free, and beginner-friendly.">
  <meta property="og:image" content="https://www.webtoolskit.org/images/image-tools-og.jpg">

  <!-- Page-specific Twitter Card Meta Tags -->
  <meta name="twitter:url" content="https://www.webtoolskit.org/p/image-editing-tools.html">
  <meta name="twitter:title" content="Free Image Editing Tools - Online Image Editor">
  <meta name="twitter:description" content="Edit images online with ease. Convert formats, crop, resize, rotate, or compress images instantly. No software required — fast, free, and beginner-friendly.">
  <meta name="twitter:image" content="https://www.webtoolskit.org/images/image-tools-og.jpg">

  <!-- Enhanced Schema.org structured data -->
  <script type="application/ld+json">
  {
    "@context": "https://schema.org",
    "@type": ["WebPage", "CollectionPage"],
    "name": "Free Image Editing Tools – Resize, Convert, Compress & Edit Online",
    "description": "Edit images online with ease. Convert formats, crop, resize, rotate, or compress images instantly. No software required — fast, free, and beginner-friendly.",
    "url": "https://www.webtoolskit.org/p/image-editing-tools.html",
    "isAccessibleForFree": true,
    "datePublished": "2025-06-21",
    "dateModified": "2025-06-21",
    "author": {
      "@type": "Organization",
      "name": "WebToolsKit",
      "url": "https://www.webtoolskit.org",
      "logo": { "@type": "ImageObject", "url": "https://www.webtoolskit.org/images/logo.png" }
    },
    "publisher": { "@type": "Organization", "name": "WebToolsKit", "url": "https://www.webtoolskit.org" },
    "mainEntity": {
      "@type": "ItemList",
      "name": "Image Editing Tools Collection",
      "description": "Comprehensive collection of free online image editing and conversion tools.",
      "numberOfItems": 23,
      "itemListElement": [
        { "@type": "WebApplication", "position": 1, "name": "ICO to PNG Converter", "description": "Convert ICO icon files to PNG format with transparency support and high quality output.", "url": "https://www.webtoolskit.org/p/ico-to-png.html", "applicationCategory": "MultimediaApplication", "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" }, "featureList": ["ICO to PNG conversion", "Transparency support", "High quality output"] },
        { "@type": "WebApplication", "position": 2, "name": "ICO Converter", "description": "Convert images to ICO format for creating website favicons and application icons.", "url": "https://www.webtoolskit.org/p/ico-converter.html", "applicationCategory": "MultimediaApplication", "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" }, "featureList": ["Image to ICO conversion", "Favicon generation", "Application icon creation"] },
        { "@type": "WebApplication", "position": 3, "name": "Image to Base64 Converter", "description": "Convert images to Base64 encoded strings for embedding in HTML, CSS, or data URIs.", "url": "https://www.webtoolskit.org/p/image-to-base64.html", "applicationCategory": "DeveloperTool", "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" }, "featureList": ["Image to Base64 encoding", "Data URI generation", "CSS embedding"] },
        { "@type": "WebApplication", "position": 4, "name": "Base64 to Image Converter", "description": "Decode Base64 strings back to image files and download them in various formats.", "url": "https://www.webtoolskit.org/p/base64-to-image.html", "applicationCategory": "DeveloperTool", "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" }, "featureList": ["Base64 to Image decoding", "Image preview", "Multiple download formats"] },
        { "@type": "WebApplication", "position": 5, "name": "Flip Image", "description": "Flip images horizontally or vertically to create mirror effects and correct orientations.", "url": "https://www.webtoolskit.org/p/flip-image.html", "applicationCategory": "MultimediaApplication", "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" }, "featureList": ["Horizontal flip", "Vertical flip", "Mirror effect"] },
        { "@type": "WebApplication", "position": 6, "name": "Rotate Image", "description": "Rotate images by any angle to fix orientation or create artistic effects.", "url": "https://www.webtoolskit.org/p/rotate-image.html", "applicationCategory": "MultimediaApplication", "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" }, "featureList": ["Custom angle rotation", "90/180/270 degree presets", "Orientation correction"] },
        { "@type": "WebApplication", "position": 7, "name": "Image Enlarger", "description": "Enlarge images while maintaining quality using advanced upscaling algorithms.", "url": "https://www.webtoolskit.org/p/image-enlarger.html", "applicationCategory": "MultimediaApplication", "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" }, "featureList": ["AI upscaling", "Resolution enhancement", "Quality preservation"] },
        { "@type": "WebApplication", "position": 8, "name": "Image Cropper", "description": "Crop images to focus on specific areas or adjust aspect ratios for different platforms.", "url": "https://www.webtoolskit.org/p/image-cropper.html", "applicationCategory": "MultimediaApplication", "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" }, "featureList": ["Freeform crop", "Aspect ratio presets", "Social media sizes"] },
        { "@type": "WebApplication", "position": 9, "name": "Image Resizer", "description": "Resize images to specific dimensions while maintaining aspect ratio and quality.", "url": "https://www.webtoolskit.org/p/image-resizer.html", "applicationCategory": "MultimediaApplication", "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" }, "featureList": ["Pixel-perfect resizing", "Percentage scaling", "Aspect ratio lock"] },
        { "@type": "WebApplication", "position": 10, "name": "Image Converter", "description": "Convert between multiple image formats including JPG, PNG, WebP, GIF, and more.", "url": "https://www.webtoolskit.org/p/image-converter.html", "applicationCategory": "MultimediaApplication", "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" }, "featureList": ["Multi-format conversion", "Batch processing", "Quality settings"] },
        { "@type": "WebApplication", "position": 11, "name": "JPG to PNG Converter", "description": "Convert JPG images to PNG format with transparency support and lossless quality.", "url": "https://www.webtoolskit.org/p/jpg-to-png.html", "applicationCategory": "MultimediaApplication", "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" }, "featureList": ["JPG to PNG conversion", "Transparency creation", "Lossless quality"] },
        { "@type": "WebApplication", "position": 12, "name": "PNG to JPG Converter", "description": "Convert PNG images to JPG format with customizable quality and background color options.", "url": "https://www.webtoolskit.org/p/png-to-jpg.html", "applicationCategory": "MultimediaApplication", "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" }, "featureList": ["PNG to JPG conversion", "Quality control", "Custom background color"] },
        { "@type": "WebApplication", "position": 13, "name": "JPG Converter", "description": "Convert JPG images to multiple formats including PNG, WebP, BMP, GIF, and ICO.", "url": "https://www.webtoolskit.org/p/jpg-converter.html", "applicationCategory": "MultimediaApplication", "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" }, "featureList": ["JPG to PNG/WebP/BMP/GIF/ICO", "Bulk conversion", "Format flexibility"] },
        { "@type": "WebApplication", "position": 14, "name": "WebP to JPG Converter", "description": "Convert WebP images to JPG format with high quality and customizable compression settings.", "url": "https://www.webtoolskit.org/p/webp-to-jpg.html", "applicationCategory": "MultimediaApplication", "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" }, "featureList": ["WebP to JPG conversion", "High compatibility", "Quality settings"] },
        { "@type": "WebApplication", "position": 15, "name": "PNG to WebP Converter", "description": "Convert PNG images to WebP format for better compression and faster web loading.", "url": "https://www.webtoolskit.org/p/png-to-webp.html", "applicationCategory": "MultimediaApplication", "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" }, "featureList": ["PNG to WebP conversion", "Web optimization", "Superior compression"] },
        { "@type": "WebApplication", "position": 16, "name": "PNG to BMP Converter", "description": "Convert PNG images to BMP format for compatibility with older systems and applications.", "url": "https://www.webtoolskit.org/p/png-to-bmp.html", "applicationCategory": "MultimediaApplication", "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" }, "featureList": ["PNG to BMP conversion", "Legacy format support", "Uncompressed output"] },
        { "@type": "WebApplication", "position": 17, "name": "PNG to GIF Converter", "description": "Convert PNG images to GIF format with support for transparency and animation.", "url": "https://www.webtoolskit.org/p/png-to-gif.html", "applicationCategory": "MultimediaApplication", "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" }, "featureList": ["PNG to GIF conversion", "Animation support", "Transparency preservation"] },
        { "@type": "WebApplication", "position": 18, "name": "PNG to ICO Converter", "description": "Convert PNG images to ICO format for creating website favicons and application icons.", "url": "https://www.webtoolskit.org/p/png-to-ico.html", "applicationCategory": "MultimediaApplication", "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" }, "featureList": ["PNG to ICO conversion", "Favicon creation", "Multiple icon sizes"] },
        { "@type": "WebApplication", "position": 19, "name": "JPG to WebP Converter", "description": "Convert JPG images to WebP format for superior compression and web performance.", "url": "https://www.webtoolskit.org/p/jpg-to-webp.html", "applicationCategory": "MultimediaApplication", "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" }, "featureList": ["JPG to WebP conversion", "Web performance", "File size reduction"] },
        { "@type": "WebApplication", "position": 20, "name": "JPG to BMP Converter", "description": "Convert JPG images to BMP format for uncompressed storage and legacy compatibility.", "url": "https://www.webtoolskit.org/p/jpg-to-bmp.html", "applicationCategory": "MultimediaApplication", "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" }, "featureList": ["JPG to BMP conversion", "Uncompressed format", "Legacy support"] },
        { "@type": "WebApplication", "position": 21, "name": "JPG to GIF Converter", "description": "Convert JPG images to GIF format with color optimization and transparency options.", "url": "https://www.webtoolskit.org/p/jpg-to-gif.html", "applicationCategory": "MultimediaApplication", "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" }, "featureList": ["JPG to GIF conversion", "Color optimization", "Animated GIF support"] },
        { "@type": "WebApplication", "position": 22, "name": "JPG to ICO Converter", "description": "Convert JPG images to ICO format for creating favicons and application icons.", "url": "https://www.webtoolskit.org/p/jpg-to-ico.html", "applicationCategory": "MultimediaApplication", "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" }, "featureList": ["JPG to ICO conversion", "Favicon generation", "Icon creation"] },
        { "@type": "WebApplication", "position": 23, "name": "WebP to PNG Converter", "description": "Convert WebP images to PNG format with transparency preservation and lossless quality.", "url": "https://www.webtoolskit.org/p/webp-to-png.html", "applicationCategory": "MultimediaApplication", "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" }, "featureList": ["WebP to PNG conversion", "Lossless quality", "Universal compatibility"] }
      ]
    },
    "breadcrumb": {
      "@type": "BreadcrumbList",
      "itemListElement": [
        { "@type": "ListItem", "position": 1, "name": "Home", "item": "https://www.webtoolskit.org" },
        { "@type": "ListItem", "position": 2, "name": "Image Editing Tools" }
      ]
    }
  }
  </script>

  <style>
    /* Duplicate CSS variables and base styles removed - inherit from main template */

    /* Page-specific styles only */
    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 8px 16px;
    }

    .page-header {
      text-align: center;
      margin-bottom: 20px;
    }

    .page-title {
      font-size: 36px;
      font-weight: 700;
      color: var(--primary-color);
      margin-bottom: 10px;
      line-height: 1.2;
    }

    .page-description {
      font-size: 1.1rem;
      color: var(--text-color-light);
      max-width: 600px;
      margin: 0 auto;
      line-height: 1.5;
    }

    .tools-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
      gap: 16px;
      justify-items: center;
    }

    .tool-card {
      background: var(--card-bg);
      border: 1px solid var(--border-color);
      border-radius: 10px;
      padding: 18px;
      text-align: center;
      transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
      box-shadow: 0 2px 8px rgba(0,0,0,0.08);
      position: relative;
      overflow: hidden;
      cursor: pointer;
    }

    .tool-card::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 4px;
      background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
      opacity: 0;
      transition: opacity 0.4s ease;
    }

    .tool-card::after {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
      transition: left 0.6s ease;
    }

    .tool-card:hover {
      transform: translateY(-8px) scale(1.02);
      box-shadow: 0 12px 35px rgba(0,0,0,0.2);
      border-color: var(--primary-color);
    }

    .tool-card:hover::before {
      opacity: 1;
    }

    .tool-card:hover::after {
      left: 100%;
    }

    .tool-icon {
      width: 56px;
      height: 56px;
      border-radius: 10px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0 auto 12px;
      font-size: 22px;
      transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
      position: relative;
      overflow: hidden;
    }

    .tool-icon::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(135deg, rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0.1));
      opacity: 0;
      transition: opacity 0.3s ease;
    }

    .tool-card:hover .tool-icon {
      transform: scale(1.1) rotate(5deg);
      box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
    }

    .tool-card:hover .tool-icon::before {
      opacity: 1;
    }

    /* Distinctive Icon Colors for Image Tools */
    .icon-ico-png { background: linear-gradient(135deg, #2563eb, #1d4ed8); color: white; }
    .icon-ico-converter { background: linear-gradient(135deg, #8B5CF6, #7C3AED); color: white; }
    .icon-image-base64 { background: linear-gradient(135deg, #EC4899, #DB2777); color: white; }
    .icon-base64-image { background: linear-gradient(135deg, #10B981, #059669); color: white; }
    .icon-flip-image { background: linear-gradient(135deg, #F59E0B, #D97706); color: white; }
    .icon-rotate-image { background: linear-gradient(135deg, #6366F1, #4F46E5); color: white; }
    .icon-image-enlarger { background: linear-gradient(135deg, #0EA5E9, #0284C7); color: white; }
    .icon-image-cropper { background: linear-gradient(135deg, #4F46E5, #4338CA); color: white; }
    .icon-image-resizer { background: linear-gradient(135deg, #EF4444, #DC2626); color: white; }
    .icon-image-converter { background: linear-gradient(135deg, #14B8A6, #0D9488); color: white; }
    .icon-jpg-png { background: linear-gradient(135deg, #8B5CF6, #7C3AED); color: white; }
    .icon-png-jpg { background: linear-gradient(135deg, #F97316, #EA580C); color: white; }
    .icon-jpg-converter { background: linear-gradient(135deg, #06B6D4, #0891B2); color: white; }
    .icon-webp-jpg { background: linear-gradient(135deg, #84CC16, #65A30D); color: white; }
    .icon-png-webp { background: linear-gradient(135deg, #EC4899, #DB2777); color: white; }
    .icon-png-bmp { background: linear-gradient(135deg, #6366F1, #4F46E5); color: white; }
    .icon-png-gif { background: linear-gradient(135deg, #F59E0B, #D97706); color: white; }
    .icon-png-ico { background: linear-gradient(135deg, #10B981, #059669); color: white; }
    .icon-jpg-webp { background: linear-gradient(135deg, #8B5CF6, #7C3AED); color: white; }
    .icon-jpg-bmp { background: linear-gradient(135deg, #EF4444, #DC2626); color: white; }
    .icon-jpg-gif { background: linear-gradient(135deg, #F97316, #EA580C); color: white; }
    .icon-jpg-ico { background: linear-gradient(135deg, #14B8A6, #0D9488); color: white; }
    .icon-webp-png { background: linear-gradient(135deg, #A855F7, #9333EA); color: white; }

    .tool-title {
      font-size: 18px;
      font-weight: 600;
      color: var(--text-color);
      margin-bottom: 8px;
    }

    .tool-description {
      color: var(--text-color-light);
      font-size: 14px;
      margin-bottom: 12px;
      line-height: 1.4;
    }

    .tool-link {
      display: inline-block;
      background: var(--primary-color);
      color: #ffffff !important;
      text-decoration: none;
      padding: 12px 24px;
      border-radius: 8px;
      font-weight: 600;
      font-size: 14px;
      transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
      border: 2px solid var(--primary-color);
      box-shadow: 0 2px 8px rgba(0, 71, 171, 0.2);
      position: relative;
      overflow: hidden;
    }

    .tool-link::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
      transition: left 0.5s ease;
    }

    .tool-link:hover {
      background: #003d96;
      border-color: #003d96;
      transform: translateY(-3px) scale(1.05);
      box-shadow: 0 8px 25px rgba(0, 71, 171, 0.4);
      color: #ffffff !important;
    }

    .tool-link:hover::before {
      left: 100%;
    }

    [data-theme="dark"] .tool-link {
      background: #60a5fa;
      border-color: #60a5fa;
      color: #ffffff !important;
      box-shadow: 0 2px 8px rgba(96, 165, 250, 0.2);
    }

    [data-theme="dark"] .tool-link:hover {
      background: #3b82f6;
      border-color: #3b82f6;
      box-shadow: 0 4px 12px rgba(96, 165, 250, 0.3);
      color: #ffffff !important;
    }

    /* Mobile Optimization with Scrollable Icons */
    @media (max-width: 768px) {
      .container { padding: 6px 12px; }
      .page-header { margin-bottom: 16px; }
      .page-title { font-size: 28px; margin-bottom: 8px; }
      .page-description { font-size: 1rem; padding: 0 8px; }

      /* Mobile single column layout */
      .tools-grid {
        display: block;
        overflow-x: unset;
        padding: 8px 0;
      }
      .tool-card {
        width: 100%;
        margin: 0 0 12px 0;
        border-radius: 14px;
        padding: 16px;
        min-height: 80px;
        box-sizing: border-box;
      }
      .tool-card .tool-icon {
        width: 48px;
        height: 48px;
        margin: 0 auto 8px;
        font-size: 20px;
      }
      .tool-card .tool-title,
      .tool-card .tool-link {
        opacity: 1;
        transform: none;
        pointer-events: auto;
      }
      .tool-card .tool-description {
        opacity: 0;
        max-height: 0;
        overflow: hidden;
        margin: 0;
        transition: opacity 0.3s, max-height 0.3s;
        will-change: opacity, max-height;
        display: block;
      }
      .tool-card.show-description .tool-description {
        opacity: 1;
        max-height: 100px;
        margin-bottom: 10px;
      }
    }
    @media (max-width: 480px) {
      .tool-card { min-width: 200px; max-width: 95vw; padding: 10px; }
      .tool-card .tool-icon { width: 40px; height: 40px; font-size: 18px; }
    }
    @media (max-width: 320px) {
      .tool-card { min-width: 140px; padding: 6px; }
      .tool-card .tool-icon { width: 32px; height: 32px; font-size: 15px; }
    }
  </style>
</head>
<body>
  <div class="container">
    <header class="page-header">
      <h1 id="main-title" class="page-title">Free Online Image Editing Tools – Resize, Convert, Crop &amp; More</h1>
      <p class="page-description">Edit images online with ease. Convert formats, crop, resize, rotate, or compress images instantly. No software required — fast, free, and beginner-friendly.</p>
    </header>

    <main id="main-content" role="main" aria-labelledby="main-title">
      <section class="tools-section" aria-labelledby="tools-section-title">
        <h2 id="tools-section-title" class="sr-only">Available Image Editing Tools</h2>
        <div class="tools-grid" role="list">
          <!-- ICO to PNG -->
          <article class="tool-card" role="listitem">
            <div class="tool-icon icon-ico-png" aria-hidden="true"><i class="fas fa-exchange-alt"></i></div>
            <div class="tool-content">
              <h3 class="tool-title">ICO to PNG</h3>
              <p class="tool-description">Convert ICO icon files to PNG format with transparency support and high quality output.</p>
              <a class="tool-link" href="/p/ico-to-png.html" rel="noopener">Try this tool →</a>
            </div>
          </article>
          <!-- ICO Converter -->
          <article class="tool-card" role="listitem">
            <div class="tool-icon icon-ico-converter" aria-hidden="true"><i class="fas fa-icons"></i></div>
            <div class="tool-content">
              <h3 class="tool-title">ICO Converter</h3>
              <p class="tool-description">Convert images to ICO format for creating website favicons and application icons.</p>
              <a class="tool-link" href="/p/ico-converter.html" rel="noopener">Try this tool →</a>
            </div>
          </article>
          <!-- Image to Base64 -->
          <article class="tool-card" role="listitem">
            <div class="tool-icon icon-image-base64" aria-hidden="true"><i class="fas fa-code"></i></div>
            <div class="tool-content">
              <h3 class="tool-title">Image to Base64</h3>
              <p class="tool-description">Convert images to Base64 encoded strings for embedding in HTML, CSS, or data URIs.</p>
              <a class="tool-link" href="/p/image-to-base64.html" rel="noopener">Try this tool →</a>
            </div>
          </article>
          <!-- Base64 to Image -->
          <article class="tool-card" role="listitem">
            <div class="tool-icon icon-base64-image" aria-hidden="true"><i class="fas fa-file-image"></i></div>
            <div class="tool-content">
              <h3 class="tool-title">Base64 to Image</h3>
              <p class="tool-description">Decode Base64 strings back to image files and download them in various formats.</p>
              <a class="tool-link" href="/p/base64-to-image.html" rel="noopener">Try this tool →</a>
            </div>
          </article>
          <!-- Flip Image -->
          <article class="tool-card" role="listitem">
            <div class="tool-icon icon-flip-image" aria-hidden="true"><i class="fas fa-arrows-alt-h"></i></div>
            <div class="tool-content">
              <h3 class="tool-title">Flip Image</h3>
              <p class="tool-description">Flip images horizontally or vertically to create mirror effects and correct orientations.</p>
              <a class="tool-link" href="/p/flip-image.html" rel="noopener">Try this tool →</a>
            </div>
          </article>
          <!-- Rotate Image -->
          <article class="tool-card" role="listitem">
            <div class="tool-icon icon-rotate-image" aria-hidden="true"><i class="fas fa-redo"></i></div>
            <div class="tool-content">
              <h3 class="tool-title">Rotate Image</h3>
              <p class="tool-description">Rotate images by any angle to fix orientation or create artistic effects.</p>
              <a class="tool-link" href="/p/rotate-image.html" rel="noopener">Try this tool →</a>
            </div>
          </article>
          <!-- Image Enlarger -->
          <article class="tool-card" role="listitem">
            <div class="tool-icon icon-image-enlarger" aria-hidden="true"><i class="fas fa-search-plus"></i></div>
            <div class="tool-content">
              <h3 class="tool-title">Image Enlarger</h3>
              <p class="tool-description">Enlarge images while maintaining quality using advanced upscaling algorithms.</p>
              <a class="tool-link" href="/p/image-enlarger.html" rel="noopener">Try this tool →</a>
            </div>
          </article>
          <!-- Image Cropper -->
          <article class="tool-card" role="listitem">
            <div class="tool-icon icon-image-cropper" aria-hidden="true"><i class="fas fa-crop"></i></div>
            <div class="tool-content">
              <h3 class="tool-title">Image Cropper</h3>
              <p class="tool-description">Crop images to focus on specific areas or adjust aspect ratios for different platforms.</p>
              <a class="tool-link" href="/p/image-cropper.html" rel="noopener">Try this tool →</a>
            </div>
          </article>
          <!-- Image Resizer -->
          <article class="tool-card" role="listitem">
            <div class="tool-icon icon-image-resizer" aria-hidden="true"><i class="fas fa-expand-arrows-alt"></i></div>
            <div class="tool-content">
              <h3 class="tool-title">Image Resizer</h3>
              <p class="tool-description">Resize images to specific dimensions while maintaining aspect ratio and quality.</p>
              <a class="tool-link" href="/p/image-resizer.html" rel="noopener">Try this tool →</a>
            </div>
          </article>
          <!-- Image Converter -->
          <article class="tool-card" role="listitem">
            <div class="tool-icon icon-image-converter" aria-hidden="true"><i class="fas fa-sync-alt"></i></div>
            <div class="tool-content">
              <h3 class="tool-title">Image Converter</h3>
              <p class="tool-description">Convert between multiple image formats including JPG, PNG, WebP, GIF, and more.</p>
              <a class="tool-link" href="/p/image-converter.html" rel="noopener">Try this tool →</a>
            </div>
          </article>
          <!-- JPG to PNG -->
          <article class="tool-card" role="listitem">
            <div class="tool-icon icon-jpg-png" aria-hidden="true"><i class="fas fa-file-export"></i></div>
            <div class="tool-content">
              <h3 class="tool-title">JPG to PNG</h3>
              <p class="tool-description">Convert JPG images to PNG format with transparency support and lossless quality.</p>
              <a class="tool-link" href="/p/jpg-to-png.html" rel="noopener">Try this tool →</a>
            </div>
          </article>
          <!-- PNG to JPG -->
          <article class="tool-card" role="listitem">
            <div class="tool-icon icon-png-jpg" aria-hidden="true"><i class="fas fa-file-import"></i></div>
            <div class="tool-content">
              <h3 class="tool-title">PNG to JPG</h3>
              <p class="tool-description">Convert PNG images to JPG format with customizable quality and background color options.</p>
              <a class="tool-link" href="/p/png-to-jpg.html" rel="noopener">Try this tool →</a>
            </div>
          </article>
          <!-- JPG Converter -->
          <article class="tool-card" role="listitem">
            <div class="tool-icon icon-jpg-converter" aria-hidden="true"><i class="fas fa-file-export"></i></div>
            <div class="tool-content">
              <h3 class="tool-title">JPG Converter</h3>
              <p class="tool-description">Convert JPG images to multiple formats including PNG, WebP, BMP, GIF, and ICO.</p>
              <a class="tool-link" href="/p/jpg-converter.html" rel="noopener">Try this tool →</a>
            </div>
          </article>
          <!-- WebP to JPG -->
          <article class="tool-card" role="listitem">
            <div class="tool-icon icon-webp-jpg" aria-hidden="true"><i class="fas fa-exchange-alt"></i></div>
            <div class="tool-content">
              <h3 class="tool-title">WebP to JPG</h3>
              <p class="tool-description">Convert WebP images to JPG format with high quality and customizable compression settings.</p>
              <a class="tool-link" href="/p/webp-to-jpg.html" rel="noopener">Try this tool →</a>
            </div>
          </article>
          <!-- PNG to WebP -->
          <article class="tool-card" role="listitem">
            <div class="tool-icon icon-png-webp" aria-hidden="true"><i class="fas fa-compress-alt"></i></div>
            <div class="tool-content">
              <h3 class="tool-title">PNG to WebP</h3>
              <p class="tool-description">Convert PNG images to WebP format for better compression and faster web loading.</p>
              <a class="tool-link" href="/p/png-to-webp.html" rel="noopener">Try this tool →</a>
            </div>
          </article>
          <!-- PNG to BMP -->
          <article class="tool-card" role="listitem">
            <div class="tool-icon icon-png-bmp" aria-hidden="true"><i class="fas fa-file-image"></i></div>
            <div class="tool-content">
              <h3 class="tool-title">PNG to BMP</h3>
              <p class="tool-description">Convert PNG images to BMP format for compatibility with older systems and applications.</p>
              <a class="tool-link" href="/p/png-to-bmp.html" rel="noopener">Try this tool →</a>
            </div>
          </article>
          <!-- PNG to GIF -->
          <article class="tool-card" role="listitem">
            <div class="tool-icon icon-png-gif" aria-hidden="true"><i class="fas fa-film"></i></div>
            <div class="tool-content">
              <h3 class="tool-title">PNG to GIF</h3>
              <p class="tool-description">Convert PNG images to GIF format with support for transparency and animation.</p>
              <a class="tool-link" href="/p/png-to-gif.html" rel="noopener">Try this tool →</a>
            </div>
          </article>
          <!-- PNG to ICO -->
          <article class="tool-card" role="listitem">
            <div class="tool-icon icon-png-ico" aria-hidden="true"><i class="fas fa-icons"></i></div>
            <div class="tool-content">
              <h3 class="tool-title">PNG to ICO</h3>
              <p class="tool-description">Convert PNG images to ICO format for creating website favicons and application icons.</p>
              <a class="tool-link" href="/p/png-to-ico.html" rel="noopener">Try this tool →</a>
            </div>
          </article>
          <!-- JPG to WebP -->
          <article class="tool-card" role="listitem">
            <div class="tool-icon icon-jpg-webp" aria-hidden="true"><i class="fas fa-compress"></i></div>
            <div class="tool-content">
              <h3 class="tool-title">JPG to WebP</h3>
              <p class="tool-description">Convert JPG images to WebP format for superior compression and web performance.</p>
              <a class="tool-link" href="/p/jpg-to-webp.html" rel="noopener">Try this tool →</a>
            </div>
          </article>
          <!-- JPG to BMP -->
          <article class="tool-card" role="listitem">
            <div class="tool-icon icon-jpg-bmp" aria-hidden="true"><i class="fas fa-file-image"></i></div>
            <div class="tool-content">
              <h3 class="tool-title">JPG to BMP</h3>
              <p class="tool-description">Convert JPG images to BMP format for uncompressed storage and legacy compatibility.</p>
              <a class="tool-link" href="/p/jpg-to-bmp.html" rel="noopener">Try this tool →</a>
            </div>
          </article>
          <!-- JPG to GIF -->
          <article class="tool-card" role="listitem">
            <div class="tool-icon icon-jpg-gif" aria-hidden="true"><i class="fas fa-film"></i></div>
            <div class="tool-content">
              <h3 class="tool-title">JPG to GIF</h3>
              <p class="tool-description">Convert JPG images to GIF format with color optimization and transparency options.</p>
              <a class="tool-link" href="/p/jpg-to-gif.html" rel="noopener">Try this tool →</a>
            </div>
          </article>
          <!-- JPG to ICO -->
          <article class="tool-card" role="listitem">
            <div class="tool-icon icon-jpg-ico" aria-hidden="true"><i class="fas fa-icons"></i></div>
            <div class="tool-content">
              <h3 class="tool-title">JPG to ICO</h3>
              <p class="tool-description">Convert JPG images to ICO format for creating favicons and application icons.</p>
              <a class="tool-link" href="/p/jpg-to-ico.html" rel="noopener">Try this tool →</a>
            </div>
          </article>
          <!-- WebP to PNG -->
          <article class="tool-card" role="listitem">
            <div class="tool-icon icon-webp-png" aria-hidden="true"><i class="fas fa-exchange-alt"></i></div>
            <div class="tool-content">
              <h3 class="tool-title">WebP to PNG</h3>
              <p class="tool-description">Convert WebP images to PNG format with transparency preservation and lossless quality.</p>
              <a class="tool-link" href="/p/webp-to-png.html" rel="noopener">Try this tool →</a>
            </div>
          </article>
        </div>
      </section>
    </main>
  </div>

  <script>
    // --- SIMPLIFIED: Expand card on tap for mobile ---
    document.addEventListener('DOMContentLoaded', function() {
      if (window.innerWidth > 768) return;
      const toolCards = document.querySelectorAll('.tool-card');
      toolCards.forEach(card => {
        card.addEventListener('click', function() {
          toolCards.forEach(c => c.classList.remove('expanded'));
          this.classList.add('expanded');
        });
      });
    });

    // Mobile: Description toggles on click/tap, always works instantly
    document.addEventListener('DOMContentLoaded', function() {
      if (window.innerWidth > 768) return;
      const toolCards = document.querySelectorAll('.tool-card');
      // Prevent double event firing on touch devices
      let lastTouch = 0;
      toolCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
          this.classList.add('show-description');
        });
        card.addEventListener('mouseleave', function() {
          this.classList.remove('show-description');
        });
        card.addEventListener('touchend', function(e) {
          e.preventDefault();
          lastTouch = Date.now();
          if (this.classList.contains('show-description')) {
            this.classList.remove('show-description');
          } else {
            toolCards.forEach(c => c.classList.remove('show-description'));
            this.classList.add('show-description');
          }
        }, { passive: false });
        card.addEventListener('click', function(e) {
          // Ignore click if just handled by touch
          if (Date.now() - lastTouch < 500) return;
          if (this.classList.contains('show-description')) {
            this.classList.remove('show-description');
          } else {
            toolCards.forEach(c => c.classList.remove('show-description'));
            this.classList.add('show-description');
          }
        });
      });
    });
  </script>

</body>
</html>